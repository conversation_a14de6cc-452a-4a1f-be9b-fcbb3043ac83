import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/services.dart';

import '../../../../core/services/ai_generation_service.dart';
import '../../../../core/services/image_save_service.dart';
import '../../../../shared/constants/app_constants.dart';
import '../../../../core/theme/app_typography.dart';
import '../../../../shared/widgets/modern_card.dart';
import '../../../../shared/widgets/modern_button.dart';

/// 婚纱照生成结果页面
class GenerationResultScreen extends StatefulWidget {
  final GenerationResult result;

  const GenerationResultScreen({
    super.key,
    required this.result,
  });

  @override
  State<GenerationResultScreen> createState() => _GenerationResultScreenState();
}

class _GenerationResultScreenState extends State<GenerationResultScreen>
    with TickerProviderStateMixin {
  bool _isSaving = false;
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startAnimations() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _fadeController.forward();
        _slideController.forward();
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          '生成结果',
          style: AppTypography.headlineSmall.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_new,
            color: AppColors.textPrimary,
            size: 20.sp,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            padding: EdgeInsets.symmetric(
              horizontal: AppSpacing.md.w,
              vertical: AppSpacing.sm.h,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (widget.result.success) ...[
                  // 生成的婚纱照
                  _buildGeneratedImage(),

                  SizedBox(height: AppSpacing.lg.h),

                  // AI分析结果
                  _buildAnalysisSection(),

                  SizedBox(height: AppSpacing.lg.h),

                  // 操作按钮
                  _buildActionButtons(context),

                  SizedBox(height: AppSpacing.xl.h),
                ] else ...[
                  // 错误状态
                  _buildErrorState(context),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建生成的图片展示
  Widget _buildGeneratedImage() {
    return ModernCard.elevated(
      padding: EdgeInsets.zero,
      margin: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 图片标题
          Padding(
            padding: EdgeInsets.all(AppSpacing.md.w),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(AppSpacing.xs.w),
                  decoration: BoxDecoration(
                    color: AppColors.primarySurface,
                    borderRadius: BorderRadius.circular(AppRadius.sm.r),
                  ),
                  child: Icon(
                    Icons.auto_awesome,
                    color: AppColors.primary,
                    size: 20.sp,
                  ),
                ),
                SizedBox(width: AppSpacing.sm.w),
                Text(
                  'AI 生成的婚纱照',
                  style: AppTypography.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.sm.w,
                    vertical: AppSpacing.xs.h,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.successSurface,
                    borderRadius: BorderRadius.circular(AppRadius.full.r),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: AppColors.success,
                        size: 12.sp,
                      ),
                      SizedBox(width: AppSpacing.xs.w),
                      Text(
                        '生成完成',
                        style: AppTypography.labelSmall.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 图片容器
          Container(
            width: double.infinity,
            margin: EdgeInsets.symmetric(horizontal: AppSpacing.md.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppRadius.lg.r),
              boxShadow: AppShadows.medium,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(AppRadius.lg.r),
              child: AspectRatio(
                aspectRatio: 3 / 4, // 婚纱照常用比例
                child: CachedNetworkImage(
                  imageUrl: widget.result.generatedImageUrl,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: AppColors.surfaceVariant,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: EdgeInsets.all(AppSpacing.md.w),
                            decoration: BoxDecoration(
                              color: AppColors.primarySurface,
                              borderRadius: BorderRadius.circular(AppRadius.full.r),
                            ),
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.primary,
                              ),
                              strokeWidth: 3,
                            ),
                          ),
                          SizedBox(height: AppSpacing.md.h),
                          Text(
                            '正在加载生成的婚纱照...',
                            style: AppTypography.bodyMedium.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: AppColors.errorSurface,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: EdgeInsets.all(AppSpacing.md.w),
                            decoration: BoxDecoration(
                              color: AppColors.error.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(AppRadius.full.r),
                            ),
                            child: Icon(
                              Icons.error_outline,
                              size: 48.sp,
                              color: AppColors.error,
                            ),
                          ),
                          SizedBox(height: AppSpacing.md.h),
                          Text(
                            '图片加载失败',
                            style: AppTypography.bodyMedium.copyWith(
                              color: AppColors.error,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(height: AppSpacing.xs.h),
                          Text(
                            '请检查网络连接后重试',
                            style: AppTypography.bodySmall.copyWith(
                              color: AppColors.textTertiary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),

          SizedBox(height: AppSpacing.md.h),
        ],
      ),
    );
  }

  /// 构建AI分析结果区域
  Widget _buildAnalysisSection() {
    return ModernCard.elevated(
      padding: EdgeInsets.zero,
      margin: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 分析标题
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(AppSpacing.md.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primarySurface,
                  AppColors.primarySurface.withOpacity(0.5),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppRadius.card.r),
                topRight: Radius.circular(AppRadius.card.r),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(AppSpacing.sm.w),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(AppRadius.sm.r),
                    boxShadow: AppShadows.light,
                  ),
                  child: Icon(
                    Icons.psychology,
                    color: AppColors.textOnPrimary,
                    size: 20.sp,
                  ),
                ),
                SizedBox(width: AppSpacing.sm.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'AI 智能分析',
                        style: AppTypography.titleMedium.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.primary,
                        ),
                      ),
                      Text(
                        '专业的婚纱照质量评估',
                        style: AppTypography.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.sm.w,
                    vertical: AppSpacing.xs.h,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppRadius.full.r),
                    border: Border.all(
                      color: AppColors.primary.withOpacity(0.2),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.star,
                        color: AppColors.primary,
                        size: 12.sp,
                      ),
                      SizedBox(width: AppSpacing.xs.w),
                      Text(
                        'AI 评分',
                        style: AppTypography.labelSmall.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 分析内容
          Padding(
            padding: EdgeInsets.all(AppSpacing.md.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(AppSpacing.md.w),
                  decoration: BoxDecoration(
                    color: AppColors.surfaceVariant,
                    borderRadius: BorderRadius.circular(AppRadius.md.r),
                    border: Border.all(
                      color: AppColors.border,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    widget.result.analysis,
                    style: AppTypography.bodyMedium.copyWith(
                      height: 1.6,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),

                SizedBox(height: AppSpacing.md.h),

                // 操作按钮
                Row(
                  children: [
                    Expanded(
                      child: ModernButton.outlined(
                        text: '复制分析结果',
                        icon: Icons.copy_outlined,
                        size: ModernButtonSize.small,
                        onPressed: _copyAnalysis,
                      ),
                    ),
                    SizedBox(width: AppSpacing.sm.w),
                    Expanded(
                      child: ModernButton.text(
                        text: '查看详情',
                        icon: Icons.info_outline,
                        size: ModernButtonSize.small,
                        onPressed: () => _showAnalysisDetails(context),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(BuildContext context) {
    return ModernCard.basic(
      padding: EdgeInsets.all(AppSpacing.md.w),
      margin: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 操作标题
          Row(
            children: [
              Icon(
                Icons.touch_app,
                color: AppColors.textSecondary,
                size: 20.sp,
              ),
              SizedBox(width: AppSpacing.sm.w),
              Text(
                '操作选项',
                style: AppTypography.titleSmall.copyWith(
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          SizedBox(height: AppSpacing.md.h),

          // 主要操作按钮
          Row(
            children: [
              Expanded(
                child: ModernButton.primary(
                  text: '保存到相册',
                  icon: _isSaving ? null : Icons.download_outlined,
                  isLoading: _isSaving,
                  size: ModernButtonSize.large,
                  onPressed: _isSaving ? null : () => _saveImage(context),
                ),
              ),
              SizedBox(width: AppSpacing.sm.w),
              Expanded(
                child: ModernButton.secondary(
                  text: '重新生成',
                  icon: Icons.refresh,
                  size: ModernButtonSize.large,
                  onPressed: () => _regeneratePhoto(context),
                ),
              ),
            ],
          ),

          SizedBox(height: AppSpacing.sm.h),

          // 次要操作按钮
          ModernButton.outlined(
            text: '查看原图',
            icon: Icons.zoom_in_outlined,
            size: ModernButtonSize.medium,
            isFullWidth: true,
            onPressed: () => _viewFullImage(context),
          ),

          SizedBox(height: AppSpacing.md.h),

          // 返回按钮
          ModernButton.text(
            text: '返回首页',
            icon: Icons.home_outlined,
            isFullWidth: true,
            size: ModernButtonSize.medium,
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: ModernCard.elevated(
        padding: EdgeInsets.all(AppSpacing.xl.w),
        margin: EdgeInsets.symmetric(horizontal: AppSpacing.md.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 错误图标
            Container(
              padding: EdgeInsets.all(AppSpacing.lg.w),
              decoration: BoxDecoration(
                color: AppColors.errorSurface,
                borderRadius: BorderRadius.circular(AppRadius.full.r),
              ),
              child: Icon(
                Icons.error_outline,
                size: 64.sp,
                color: AppColors.error,
              ),
            ),

            SizedBox(height: AppSpacing.lg.h),

            // 错误标题
            Text(
              '生成失败',
              style: AppTypography.headlineMedium.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: AppSpacing.sm.h),

            // 错误描述
            Text(
              widget.result.message,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: AppSpacing.xl.h),

            // 操作按钮
            Column(
              children: [
                ModernButton.primary(
                  text: '返回重试',
                  icon: Icons.refresh,
                  isFullWidth: true,
                  size: ModernButtonSize.large,
                  onPressed: () => Navigator.pop(context),
                ),

                SizedBox(height: AppSpacing.sm.h),

                ModernButton.text(
                  text: '联系客服',
                  icon: Icons.support_agent_outlined,
                  isFullWidth: true,
                  size: ModernButtonSize.medium,
                  onPressed: () => _contactSupport(context),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 复制分析结果
  void _copyAnalysis() {
    Clipboard.setData(ClipboardData(text: widget.result.analysis));
    _showSuccessSnackBar('分析结果已复制到剪贴板');
  }

  /// 显示分析详情
  void _showAnalysisDetails(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppRadius.xl.r),
            topRight: Radius.circular(AppRadius.xl.r),
          ),
        ),
        child: Column(
          children: [
            // 拖拽指示器
            Container(
              margin: EdgeInsets.only(top: AppSpacing.sm.h),
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: AppColors.neutral300,
                borderRadius: BorderRadius.circular(AppRadius.full.r),
              ),
            ),

            // 标题
            Padding(
              padding: EdgeInsets.all(AppSpacing.md.w),
              child: Text(
                'AI 分析详情',
                style: AppTypography.headlineSmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

            // 内容
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: AppSpacing.md.w),
                child: Text(
                  widget.result.analysis,
                  style: AppTypography.bodyMedium.copyWith(
                    height: 1.6,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 查看全图
  void _viewFullImage(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            iconTheme: const IconThemeData(color: Colors.white),
            title: Text(
              '查看原图',
              style: AppTypography.titleMedium.copyWith(color: Colors.white),
            ),
          ),
          body: Center(
            child: InteractiveViewer(
              child: CachedNetworkImage(
                imageUrl: widget.result.generatedImageUrl,
                fit: BoxFit.contain,
                placeholder: (context, url) => const CircularProgressIndicator(),
                errorWidget: (context, url, error) => const Icon(
                  Icons.error,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 联系客服
  void _contactSupport(BuildContext context) {
    _showInfoSnackBar('客服功能开发中...');
  }

  /// 分享结果
  void _shareResult(BuildContext context) {
    // TODO: 实现分享功能
    _showInfoSnackBar('分享功能开发中...');
  }

  /// 重新生成照片
  void _regeneratePhoto(BuildContext context) {
    // TODO: 实现重新生成功能
    _showInfoSnackBar('重新生成功能开发中...');
  }

  /// 直接保存图片到相册
  Future<void> _saveImage(BuildContext context) async {
    if (!widget.result.success || widget.result.generatedImageUrl.isEmpty) {
      _showWarningSnackBar('没有可保存的图片');
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final fileName = 'hera_wedding_photo_${DateTime.now().millisecondsSinceEpoch}';
      final success = await ImageSaveService.saveNetworkImageToGallery(
        widget.result.generatedImageUrl,
        context: context,
        fileName: fileName,
      );

      if (success && mounted) {
        _showSuccessSnackBar('图片已成功保存到相册');
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('保存失败：$e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  /// 显示成功提示
  void _showSuccessSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.textOnPrimary, size: 20.sp),
            SizedBox(width: AppSpacing.sm.w),
            Expanded(
              child: Text(
                message,
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textOnPrimary,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.sm.r),
        ),
        margin: EdgeInsets.all(AppSpacing.md.w),
      ),
    );
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error, color: AppColors.textOnPrimary, size: 20.sp),
            SizedBox(width: AppSpacing.sm.w),
            Expanded(
              child: Text(
                message,
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textOnPrimary,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.sm.r),
        ),
        margin: EdgeInsets.all(AppSpacing.md.w),
      ),
    );
  }

  /// 显示警告提示
  void _showWarningSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.warning, color: AppColors.textOnPrimary, size: 20.sp),
            SizedBox(width: AppSpacing.sm.w),
            Expanded(
              child: Text(
                message,
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textOnPrimary,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.warning,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.sm.r),
        ),
        margin: EdgeInsets.all(AppSpacing.md.w),
      ),
    );
  }

  /// 显示信息提示
  void _showInfoSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.info, color: AppColors.textOnPrimary, size: 20.sp),
            SizedBox(width: AppSpacing.sm.w),
            Expanded(
              child: Text(
                message,
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textOnPrimary,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.info,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.sm.r),
        ),
        margin: EdgeInsets.all(AppSpacing.md.w),
      ),
    );
  }


} 