import 'package:flutter/animation.dart';
import 'package:flutter/material.dart';

/// 应用常量类
abstract class AppConstants {
  AppConstants._();
}

/// 设计系统 - 颜色常量
abstract class AppColors {
  AppColors._();

  // 主色调 - 优雅的紫色系
  static const Color primary = Color(0xFF6366F1); // Indigo-500
  static const Color primaryLight = Color(0xFF818CF8); // Indigo-400
  static const Color primaryDark = Color(0xFF4F46E5); // Indigo-600
  static const Color primarySurface = Color(0xFFF0F0FF); // Indigo-50

  // 次要色调 - 温暖的粉色系
  static const Color secondary = Color(0xFFEC4899); // Pink-500
  static const Color secondaryLight = Color(0xFFF472B6); // Pink-400
  static const Color secondaryDark = Color(0xFFDB2777); // Pink-600
  static const Color secondarySurface = Color(0xFFFDF2F8); // Pink-50

  // 中性色调 - 现代灰色系
  static const Color neutral50 = Color(0xFFFAFAFA);
  static const Color neutral100 = Color(0xFFF5F5F5);
  static const Color neutral200 = Color(0xFFE5E5E5);
  static const Color neutral300 = Color(0xFFD4D4D4);
  static const Color neutral400 = Color(0xFFA3A3A3);
  static const Color neutral500 = Color(0xFF737373);
  static const Color neutral600 = Color(0xFF525252);
  static const Color neutral700 = Color(0xFF404040);
  static const Color neutral800 = Color(0xFF262626);
  static const Color neutral900 = Color(0xFF171717);

  // 语义色彩
  static const Color success = Color(0xFF10B981); // Emerald-500
  static const Color successLight = Color(0xFF34D399); // Emerald-400
  static const Color successSurface = Color(0xFFECFDF5); // Emerald-50

  static const Color warning = Color(0xFFF59E0B); // Amber-500
  static const Color warningLight = Color(0xFFFBBF24); // Amber-400
  static const Color warningSurface = Color(0xFFFFFBEB); // Amber-50

  static const Color error = Color(0xFFEF4444); // Red-500
  static const Color errorLight = Color(0xFFF87171); // Red-400
  static const Color errorSurface = Color(0xFFFEF2F2); // Red-50

  static const Color info = Color(0xFF3B82F6); // Blue-500
  static const Color infoLight = Color(0xFF60A5FA); // Blue-400
  static const Color infoSurface = Color(0xFFEFF6FF); // Blue-50

  // 表面色彩
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF8FAFC);
  static const Color background = Color(0xFFFCFCFC);
  static const Color backgroundSecondary = Color(0xFFF8FAFC);

  // 文本色彩
  static const Color textPrimary = Color(0xFF1F2937); // Gray-800
  static const Color textSecondary = Color(0xFF6B7280); // Gray-500
  static const Color textTertiary = Color(0xFF9CA3AF); // Gray-400
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textOnSecondary = Color(0xFFFFFFFF);

  // 边框色彩
  static const Color border = Color(0xFFE5E7EB); // Gray-200
  static const Color borderLight = Color(0xFFF3F4F6); // Gray-100
  static const Color borderFocus = primary;

  // 阴影色彩
  static const Color shadow = Color(0x1A000000); // 10% black
  static const Color shadowLight = Color(0x0D000000); // 5% black
  static const Color shadowMedium = Color(0x26000000); // 15% black
  static const Color shadowHeavy = Color(0x33000000); // 20% black
}

/// 设计系统 - 间距常量
abstract class AppSpacing {
  AppSpacing._();

  // 基础间距单位 (8pt grid system)
  static const double unit = 8.0;

  // 间距尺寸
  static const double xs = unit * 0.5; // 4
  static const double sm = unit * 1; // 8
  static const double md = unit * 2; // 16
  static const double lg = unit * 3; // 24
  static const double xl = unit * 4; // 32
  static const double xxl = unit * 6; // 48
  static const double xxxl = unit * 8; // 64

  // 组件间距
  static const double componentXs = xs;
  static const double componentSm = sm;
  static const double componentMd = md;
  static const double componentLg = lg;
  static const double componentXl = xl;

  // 页面边距
  static const double pageHorizontal = md;
  static const double pageVertical = lg;

  // 卡片间距
  static const double cardPadding = md;
  static const double cardMargin = sm;
  static const double cardSpacing = md;

  // 按钮间距
  static const double buttonPaddingHorizontal = lg;
  static const double buttonPaddingVertical = sm;
  static const double buttonSpacing = sm;
}

/// 设计系统 - 圆角常量
abstract class AppRadius {
  AppRadius._();

  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 12.0;
  static const double lg = 16.0;
  static const double xl = 20.0;
  static const double xxl = 24.0;
  static const double full = 999.0;

  // 组件圆角
  static const double button = md;
  static const double card = lg;
  static const double input = sm;
  static const double image = md;
  static const double dialog = lg;
}

/// 设计系统 - 阴影常量
abstract class AppShadows {
  AppShadows._();

  // 轻微阴影
  static const List<BoxShadow> light = [
    BoxShadow(
      color: AppColors.shadowLight,
      offset: Offset(0, 1),
      blurRadius: 2,
      spreadRadius: 0,
    ),
  ];

  // 中等阴影
  static const List<BoxShadow> medium = [
    BoxShadow(
      color: AppColors.shadow,
      offset: Offset(0, 4),
      blurRadius: 8,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: AppColors.shadowLight,
      offset: Offset(0, 2),
      blurRadius: 4,
      spreadRadius: 0,
    ),
  ];

  // 重阴影
  static const List<BoxShadow> heavy = [
    BoxShadow(
      color: AppColors.shadowMedium,
      offset: Offset(0, 8),
      blurRadius: 16,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: AppColors.shadowLight,
      offset: Offset(0, 4),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];

  // 卡片阴影
  static const List<BoxShadow> card = medium;

  // 按钮阴影
  static const List<BoxShadow> button = light;

  // 浮动阴影
  static const List<BoxShadow> floating = heavy;
}

/// 存储键常量
abstract class StorageKeys {
  StorageKeys._();

  /// 用户Token
  static const String authToken = 'auth_token';
  
  /// 用户信息
  static const String userInfo = 'user_info';
  
  /// 主题模式
  static const String themeMode = 'theme_mode';
  
  /// 语言
  static const String locale = 'locale';
  
  /// 是否首次启动应用
  static const String isFirstTime = 'is_first_time';
  
  /// 缓存过期时间前缀
  static const String cacheExpirePrefix = 'cache_expire_';
}

/// 持续时间常量
abstract class AppDurations {
  AppDurations._();

  /// 页面转场动画
  static const Duration pageTransition = Duration(milliseconds: 300);
  
  /// 短消息提示
  static const Duration shortMessage = Duration(seconds: 2);
  
  /// 动画
  static const Duration animation = Duration(milliseconds: 350);
  
  /// 启动页停留
  static const Duration splash = Duration(seconds: 2);
}

/// 宽高比常量
abstract class Ratios {
  Ratios._();

  /// 卡片宽高比
  static const double card = 1.5;
  
  /// 列表项宽高比
  static const double listItem = 2.5;
  
  /// 横幅广告宽高比
  static const double banner = 2.1;
}

/// 动画曲线常量
abstract class AppCurves {
  AppCurves._();

  /// 页面转场
  static const pageCurve = Cubic(0.2, 0.0, 0.0, 1.0);
}

/// 数字常量
abstract class Numbers {
  Numbers._();

  /// 最大页面大小
  static const int maxPageSize = 50;
  
  /// 默认页面大小
  static const int defaultPageSize = 20;
  
  /// 最大密码长度
  static const int maxPasswordLength = 32;
  
  /// 最小密码长度
  static const int minPasswordLength = 6;
}

/// 正则表达式常量
abstract class Regexps {
  Regexps._();

  /// 电子邮件
  static final RegExp email = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );
  
  /// 手机号码（中国）
  static final RegExp phoneNumber = RegExp(
    r'^1[3-9]\d{9}$',
  );
  
  /// 密码（至少包含一个字母和一个数字）
  static final RegExp password = RegExp(
    r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d!@#$%^&*()_+]{6,}$',
  );
} 