import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_constants.dart';

/// 现代卡片组件 - 支持多种样式和交互
class ModernCard extends StatelessWidget {
  /// 子组件
  final Widget child;
  
  /// 内边距
  final EdgeInsetsGeometry? padding;
  
  /// 外边距
  final EdgeInsetsGeometry? margin;
  
  /// 背景色
  final Color? backgroundColor;
  
  /// 边框
  final Border? border;
  
  /// 圆角
  final BorderRadius? borderRadius;
  
  /// 阴影
  final List<BoxShadow>? boxShadow;
  
  /// 点击事件
  final VoidCallback? onTap;
  
  /// 长按事件
  final VoidCallback? onLongPress;
  
  /// 是否启用涟漪效果
  final bool enableRipple;
  
  /// 卡片类型
  final ModernCardType type;
  
  /// 高度（用于 Hero 动画）
  final double? height;
  
  /// 宽度
  final double? width;
  
  /// 是否启用玻璃态效果
  final bool enableGlassmorphism;
  
  /// 玻璃态透明度
  final double glassmorphismOpacity;

  const ModernCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.border,
    this.borderRadius,
    this.boxShadow,
    this.onTap,
    this.onLongPress,
    this.enableRipple = true,
    this.type = ModernCardType.elevated,
    this.height,
    this.width,
    this.enableGlassmorphism = false,
    this.glassmorphismOpacity = 0.1,
  });

  /// 创建基础卡片
  factory ModernCard.basic({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
  }) {
    return ModernCard(
      type: ModernCardType.basic,
      padding: padding ?? EdgeInsets.all(AppSpacing.md.w),
      margin: margin ?? EdgeInsets.all(AppSpacing.sm.w),
      onTap: onTap,
      child: child,
    );
  }

  /// 创建悬浮卡片
  factory ModernCard.elevated({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
  }) {
    return ModernCard(
      type: ModernCardType.elevated,
      padding: padding ?? EdgeInsets.all(AppSpacing.md.w),
      margin: margin ?? EdgeInsets.all(AppSpacing.sm.w),
      onTap: onTap,
      child: child,
    );
  }

  /// 创建轮廓卡片
  factory ModernCard.outlined({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
  }) {
    return ModernCard(
      type: ModernCardType.outlined,
      padding: padding ?? EdgeInsets.all(AppSpacing.md.w),
      margin: margin ?? EdgeInsets.all(AppSpacing.sm.w),
      onTap: onTap,
      child: child,
    );
  }

  /// 创建玻璃态卡片
  factory ModernCard.glassmorphism({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
    double opacity = 0.1,
  }) {
    return ModernCard(
      type: ModernCardType.glassmorphism,
      padding: padding ?? EdgeInsets.all(AppSpacing.md.w),
      margin: margin ?? EdgeInsets.all(AppSpacing.sm.w),
      onTap: onTap,
      enableGlassmorphism: true,
      glassmorphismOpacity: opacity,
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    final effectivePadding = padding ?? EdgeInsets.all(AppSpacing.md.w);
    final effectiveMargin = margin ?? EdgeInsets.all(AppSpacing.sm.w);
    final effectiveBorderRadius = borderRadius ?? BorderRadius.circular(AppRadius.card.r);

    // 根据类型设置样式
    Color effectiveBackgroundColor;
    List<BoxShadow> effectiveBoxShadow;
    Border? effectiveBorder;

    switch (type) {
      case ModernCardType.basic:
        effectiveBackgroundColor = backgroundColor ?? AppColors.surface;
        effectiveBoxShadow = boxShadow ?? [];
        effectiveBorder = border;
        break;
      case ModernCardType.elevated:
        effectiveBackgroundColor = backgroundColor ?? AppColors.surface;
        effectiveBoxShadow = boxShadow ?? AppShadows.card;
        effectiveBorder = border;
        break;
      case ModernCardType.outlined:
        effectiveBackgroundColor = backgroundColor ?? AppColors.surface;
        effectiveBoxShadow = boxShadow ?? [];
        effectiveBorder = border ?? Border.all(color: AppColors.border, width: 1);
        break;
      case ModernCardType.glassmorphism:
        effectiveBackgroundColor = backgroundColor ?? 
            AppColors.surface.withOpacity(glassmorphismOpacity);
        effectiveBoxShadow = boxShadow ?? AppShadows.light;
        effectiveBorder = border ?? Border.all(
          color: AppColors.border.withOpacity(0.2), 
          width: 1,
        );
        break;
    }

    Widget cardContent = Container(
      width: width,
      height: height,
      padding: effectivePadding,
      margin: effectiveMargin,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: effectiveBorderRadius,
        boxShadow: effectiveBoxShadow,
        border: effectiveBorder,
      ),
      child: child,
    );

    // 添加玻璃态背景模糊效果
    if (enableGlassmorphism && type == ModernCardType.glassmorphism) {
      cardContent = ClipRRect(
        borderRadius: effectiveBorderRadius,
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: cardContent,
        ),
      );
    }

    // 添加交互效果
    if (onTap != null || onLongPress != null) {
      if (enableRipple) {
        return Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            onLongPress: onLongPress,
            borderRadius: effectiveBorderRadius,
            child: cardContent,
          ),
        );
      } else {
        return GestureDetector(
          onTap: onTap,
          onLongPress: onLongPress,
          child: cardContent,
        );
      }
    }

    return cardContent;
  }
}

/// 卡片类型枚举
enum ModernCardType {
  /// 基础卡片 - 无阴影
  basic,
  
  /// 悬浮卡片 - 带阴影
  elevated,
  
  /// 轮廓卡片 - 带边框
  outlined,
  
  /// 玻璃态卡片 - 半透明背景
  glassmorphism,
}
