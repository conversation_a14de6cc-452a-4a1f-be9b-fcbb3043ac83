import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_constants.dart';
import '../../core/theme/app_typography.dart';

/// 现代按钮组件 - 支持多种样式和状态
class ModernButton extends StatefulWidget {
  /// 按钮文本
  final String text;
  
  /// 点击事件
  final VoidCallback? onPressed;
  
  /// 按钮类型
  final ModernButtonType type;
  
  /// 按钮大小
  final ModernButtonSize size;
  
  /// 图标
  final IconData? icon;
  
  /// 图标位置
  final ModernButtonIconPosition iconPosition;
  
  /// 是否全宽
  final bool isFullWidth;
  
  /// 是否加载中
  final bool isLoading;
  
  /// 自定义颜色
  final Color? backgroundColor;
  final Color? foregroundColor;
  final Color? borderColor;
  
  /// 自定义圆角
  final BorderRadius? borderRadius;
  
  /// 自定义高度
  final double? height;
  
  /// 自定义宽度
  final double? width;
  
  /// 是否启用触觉反馈
  final bool enableHapticFeedback;

  const ModernButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ModernButtonType.primary,
    this.size = ModernButtonSize.medium,
    this.icon,
    this.iconPosition = ModernButtonIconPosition.left,
    this.isFullWidth = false,
    this.isLoading = false,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.borderRadius,
    this.height,
    this.width,
    this.enableHapticFeedback = true,
  });

  /// 创建主要按钮
  factory ModernButton.primary({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isFullWidth = false,
    bool isLoading = false,
    ModernButtonSize size = ModernButtonSize.medium,
  }) {
    return ModernButton(
      text: text,
      onPressed: onPressed,
      type: ModernButtonType.primary,
      icon: icon,
      isFullWidth: isFullWidth,
      isLoading: isLoading,
      size: size,
    );
  }

  /// 创建次要按钮
  factory ModernButton.secondary({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isFullWidth = false,
    bool isLoading = false,
    ModernButtonSize size = ModernButtonSize.medium,
  }) {
    return ModernButton(
      text: text,
      onPressed: onPressed,
      type: ModernButtonType.secondary,
      icon: icon,
      isFullWidth: isFullWidth,
      isLoading: isLoading,
      size: size,
    );
  }

  /// 创建轮廓按钮
  factory ModernButton.outlined({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isFullWidth = false,
    bool isLoading = false,
    ModernButtonSize size = ModernButtonSize.medium,
  }) {
    return ModernButton(
      text: text,
      onPressed: onPressed,
      type: ModernButtonType.outlined,
      icon: icon,
      isFullWidth: isFullWidth,
      isLoading: isLoading,
      size: size,
    );
  }

  /// 创建文本按钮
  factory ModernButton.text({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isFullWidth = false,
    bool isLoading = false,
    ModernButtonSize size = ModernButtonSize.medium,
  }) {
    return ModernButton(
      text: text,
      onPressed: onPressed,
      type: ModernButtonType.text,
      icon: icon,
      isFullWidth: isFullWidth,
      isLoading: isLoading,
      size: size,
    );
  }

  /// 创建危险按钮
  factory ModernButton.danger({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isFullWidth = false,
    bool isLoading = false,
    ModernButtonSize size = ModernButtonSize.medium,
  }) {
    return ModernButton(
      text: text,
      onPressed: onPressed,
      type: ModernButtonType.danger,
      icon: icon,
      isFullWidth: isFullWidth,
      isLoading: isLoading,
      size: size,
    );
  }

  @override
  State<ModernButton> createState() => _ModernButtonState();
}

class _ModernButtonState extends State<ModernButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDisabled = widget.onPressed == null || widget.isLoading;
    
    // 根据大小设置参数
    final buttonConfig = _getButtonConfig();
    
    // 根据类型设置颜色
    final colorConfig = _getColorConfig(isDisabled);

    Widget buttonChild = _buildButtonContent(buttonConfig, colorConfig);

    // 添加动画效果
    buttonChild = AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: child,
        );
      },
      child: buttonChild,
    );

    return GestureDetector(
      onTapDown: isDisabled ? null : (_) => _animationController.forward(),
      onTapUp: isDisabled ? null : (_) => _animationController.reverse(),
      onTapCancel: isDisabled ? null : () => _animationController.reverse(),
      child: buttonChild,
    );
  }

  /// 获取按钮配置
  _ButtonConfig _getButtonConfig() {
    switch (widget.size) {
      case ModernButtonSize.small:
        return _ButtonConfig(
          height: widget.height ?? 32.h,
          paddingHorizontal: 16.w,
          paddingVertical: 6.h,
          fontSize: 12.sp,
          iconSize: 16.sp,
          borderRadius: widget.borderRadius ?? BorderRadius.circular(AppRadius.sm.r),
        );
      case ModernButtonSize.medium:
        return _ButtonConfig(
          height: widget.height ?? 44.h,
          paddingHorizontal: 24.w,
          paddingVertical: 12.h,
          fontSize: 14.sp,
          iconSize: 18.sp,
          borderRadius: widget.borderRadius ?? BorderRadius.circular(AppRadius.md.r),
        );
      case ModernButtonSize.large:
        return _ButtonConfig(
          height: widget.height ?? 56.h,
          paddingHorizontal: 32.w,
          paddingVertical: 16.h,
          fontSize: 16.sp,
          iconSize: 20.sp,
          borderRadius: widget.borderRadius ?? BorderRadius.circular(AppRadius.md.r),
        );
    }
  }

  /// 获取颜色配置
  _ColorConfig _getColorConfig(bool isDisabled) {
    if (isDisabled) {
      return _ColorConfig(
        backgroundColor: AppColors.neutral200,
        foregroundColor: AppColors.neutral400,
        borderColor: AppColors.neutral200,
      );
    }

    switch (widget.type) {
      case ModernButtonType.primary:
        return _ColorConfig(
          backgroundColor: widget.backgroundColor ?? AppColors.primary,
          foregroundColor: widget.foregroundColor ?? AppColors.textOnPrimary,
          borderColor: widget.borderColor ?? AppColors.primary,
        );
      case ModernButtonType.secondary:
        return _ColorConfig(
          backgroundColor: widget.backgroundColor ?? AppColors.secondary,
          foregroundColor: widget.foregroundColor ?? AppColors.textOnSecondary,
          borderColor: widget.borderColor ?? AppColors.secondary,
        );
      case ModernButtonType.outlined:
        return _ColorConfig(
          backgroundColor: widget.backgroundColor ?? Colors.transparent,
          foregroundColor: widget.foregroundColor ?? AppColors.primary,
          borderColor: widget.borderColor ?? AppColors.primary,
        );
      case ModernButtonType.text:
        return _ColorConfig(
          backgroundColor: widget.backgroundColor ?? Colors.transparent,
          foregroundColor: widget.foregroundColor ?? AppColors.primary,
          borderColor: widget.borderColor ?? Colors.transparent,
        );
      case ModernButtonType.danger:
        return _ColorConfig(
          backgroundColor: widget.backgroundColor ?? AppColors.error,
          foregroundColor: widget.foregroundColor ?? AppColors.textOnPrimary,
          borderColor: widget.borderColor ?? AppColors.error,
        );
    }
  }

  /// 构建按钮内容
  Widget _buildButtonContent(_ButtonConfig config, _ColorConfig colorConfig) {
    final hasIcon = widget.icon != null;
    final hasText = widget.text.isNotEmpty;

    List<Widget> children = [];

    // 添加图标或加载指示器
    if (widget.isLoading) {
      children.add(
        SizedBox(
          width: config.iconSize,
          height: config.iconSize,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(colorConfig.foregroundColor),
          ),
        ),
      );
    } else if (hasIcon) {
      children.add(
        Icon(
          widget.icon,
          size: config.iconSize,
          color: colorConfig.foregroundColor,
        ),
      );
    }

    // 添加间距
    if (hasIcon && hasText && !widget.isLoading) {
      children.add(SizedBox(width: AppSpacing.sm.w));
    }

    // 添加文本
    if (hasText) {
      children.add(
        Text(
          widget.text,
          style: AppTypography.labelMedium.copyWith(
            fontSize: config.fontSize,
            color: colorConfig.foregroundColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }

    // 根据图标位置调整顺序
    if (widget.iconPosition == ModernButtonIconPosition.right && hasIcon && hasText) {
      children = children.reversed.toList();
    }

    return Container(
      width: widget.isFullWidth ? double.infinity : widget.width,
      height: config.height,
      decoration: BoxDecoration(
        color: colorConfig.backgroundColor,
        borderRadius: config.borderRadius,
        border: Border.all(color: colorConfig.borderColor),
        boxShadow: widget.type == ModernButtonType.primary || 
                   widget.type == ModernButtonType.secondary ||
                   widget.type == ModernButtonType.danger
            ? AppShadows.button
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.onPressed,
          borderRadius: config.borderRadius,
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: config.paddingHorizontal,
              vertical: config.paddingVertical,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: widget.isFullWidth ? MainAxisSize.max : MainAxisSize.min,
              children: children,
            ),
          ),
        ),
      ),
    );
  }
}

/// 按钮类型
enum ModernButtonType {
  primary,
  secondary,
  outlined,
  text,
  danger,
}

/// 按钮大小
enum ModernButtonSize {
  small,
  medium,
  large,
}

/// 图标位置
enum ModernButtonIconPosition {
  left,
  right,
}

/// 按钮配置
class _ButtonConfig {
  final double height;
  final double paddingHorizontal;
  final double paddingVertical;
  final double fontSize;
  final double iconSize;
  final BorderRadius borderRadius;

  _ButtonConfig({
    required this.height,
    required this.paddingHorizontal,
    required this.paddingVertical,
    required this.fontSize,
    required this.iconSize,
    required this.borderRadius,
  });
}

/// 颜色配置
class _ColorConfig {
  final Color backgroundColor;
  final Color foregroundColor;
  final Color borderColor;

  _ColorConfig({
    required this.backgroundColor,
    required this.foregroundColor,
    required this.borderColor,
  });
}
