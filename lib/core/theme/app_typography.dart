import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../shared/constants/app_constants.dart';

/// 应用字体系统 - 遵循 iOS HIG 和 Material Design 原则
class AppTypography {
  AppTypography._();

  // 字体家族 - 优先使用系统字体
  static const String _fontFamily = 'SF Pro Display'; // iOS 系统字体
  static const String _fallbackFontFamily = 'Roboto'; // Android 系统字体

  /// 获取字体家族
  static String get fontFamily {
    // 在实际应用中，可以根据平台选择字体
    return _fontFamily;
  }

  // 字体权重
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;

  // 行高比例
  static const double _tightLineHeight = 1.2;
  static const double _normalLineHeight = 1.4;
  static const double _relaxedLineHeight = 1.6;

  /// 标题样式 - 大标题
  static TextStyle get displayLarge => TextStyle(
    fontFamily: fontFamily,
    fontSize: 32.sp,
    fontWeight: bold,
    height: _tightLineHeight,
    color: AppColors.textPrimary,
    letterSpacing: -0.5,
  );

  static TextStyle get displayMedium => TextStyle(
    fontFamily: fontFamily,
    fontSize: 28.sp,
    fontWeight: bold,
    height: _tightLineHeight,
    color: AppColors.textPrimary,
    letterSpacing: -0.25,
  );

  static TextStyle get displaySmall => TextStyle(
    fontFamily: fontFamily,
    fontSize: 24.sp,
    fontWeight: semiBold,
    height: _tightLineHeight,
    color: AppColors.textPrimary,
    letterSpacing: 0,
  );

  /// 标题样式 - 页面标题
  static TextStyle get headlineLarge => TextStyle(
    fontFamily: fontFamily,
    fontSize: 22.sp,
    fontWeight: semiBold,
    height: _normalLineHeight,
    color: AppColors.textPrimary,
    letterSpacing: 0,
  );

  static TextStyle get headlineMedium => TextStyle(
    fontFamily: fontFamily,
    fontSize: 20.sp,
    fontWeight: semiBold,
    height: _normalLineHeight,
    color: AppColors.textPrimary,
    letterSpacing: 0.15,
  );

  static TextStyle get headlineSmall => TextStyle(
    fontFamily: fontFamily,
    fontSize: 18.sp,
    fontWeight: medium,
    height: _normalLineHeight,
    color: AppColors.textPrimary,
    letterSpacing: 0.15,
  );

  /// 标题样式 - 组件标题
  static TextStyle get titleLarge => TextStyle(
    fontFamily: fontFamily,
    fontSize: 16.sp,
    fontWeight: medium,
    height: _normalLineHeight,
    color: AppColors.textPrimary,
    letterSpacing: 0.15,
  );

  static TextStyle get titleMedium => TextStyle(
    fontFamily: fontFamily,
    fontSize: 14.sp,
    fontWeight: medium,
    height: _normalLineHeight,
    color: AppColors.textPrimary,
    letterSpacing: 0.1,
  );

  static TextStyle get titleSmall => TextStyle(
    fontFamily: fontFamily,
    fontSize: 12.sp,
    fontWeight: medium,
    height: _normalLineHeight,
    color: AppColors.textPrimary,
    letterSpacing: 0.1,
  );

  /// 正文样式
  static TextStyle get bodyLarge => TextStyle(
    fontFamily: fontFamily,
    fontSize: 16.sp,
    fontWeight: regular,
    height: _relaxedLineHeight,
    color: AppColors.textPrimary,
    letterSpacing: 0.5,
  );

  static TextStyle get bodyMedium => TextStyle(
    fontFamily: fontFamily,
    fontSize: 14.sp,
    fontWeight: regular,
    height: _relaxedLineHeight,
    color: AppColors.textPrimary,
    letterSpacing: 0.25,
  );

  static TextStyle get bodySmall => TextStyle(
    fontFamily: fontFamily,
    fontSize: 12.sp,
    fontWeight: regular,
    height: _relaxedLineHeight,
    color: AppColors.textSecondary,
    letterSpacing: 0.4,
  );

  /// 标签样式
  static TextStyle get labelLarge => TextStyle(
    fontFamily: fontFamily,
    fontSize: 14.sp,
    fontWeight: medium,
    height: _normalLineHeight,
    color: AppColors.textPrimary,
    letterSpacing: 0.1,
  );

  static TextStyle get labelMedium => TextStyle(
    fontFamily: fontFamily,
    fontSize: 12.sp,
    fontWeight: medium,
    height: _normalLineHeight,
    color: AppColors.textPrimary,
    letterSpacing: 0.5,
  );

  static TextStyle get labelSmall => TextStyle(
    fontFamily: fontFamily,
    fontSize: 10.sp,
    fontWeight: medium,
    height: _normalLineHeight,
    color: AppColors.textSecondary,
    letterSpacing: 0.5,
  );

  /// 按钮样式
  static TextStyle get buttonLarge => TextStyle(
    fontFamily: fontFamily,
    fontSize: 16.sp,
    fontWeight: semiBold,
    height: _normalLineHeight,
    color: AppColors.textOnPrimary,
    letterSpacing: 0.5,
  );

  static TextStyle get buttonMedium => TextStyle(
    fontFamily: fontFamily,
    fontSize: 14.sp,
    fontWeight: semiBold,
    height: _normalLineHeight,
    color: AppColors.textOnPrimary,
    letterSpacing: 0.25,
  );

  static TextStyle get buttonSmall => TextStyle(
    fontFamily: fontFamily,
    fontSize: 12.sp,
    fontWeight: medium,
    height: _normalLineHeight,
    color: AppColors.textOnPrimary,
    letterSpacing: 0.5,
  );

  /// 特殊样式
  static TextStyle get caption => TextStyle(
    fontFamily: fontFamily,
    fontSize: 11.sp,
    fontWeight: regular,
    height: _normalLineHeight,
    color: AppColors.textTertiary,
    letterSpacing: 0.5,
  );

  static TextStyle get overline => TextStyle(
    fontFamily: fontFamily,
    fontSize: 10.sp,
    fontWeight: medium,
    height: _normalLineHeight,
    color: AppColors.textSecondary,
    letterSpacing: 1.5,
  );

  /// 创建带颜色的文本样式
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }

  /// 创建带权重的文本样式
  static TextStyle withWeight(TextStyle style, FontWeight weight) {
    return style.copyWith(fontWeight: weight);
  }

  /// 创建带大小的文本样式
  static TextStyle withSize(TextStyle style, double size) {
    return style.copyWith(fontSize: size.sp);
  }

  /// 创建带行高的文本样式
  static TextStyle withHeight(TextStyle style, double height) {
    return style.copyWith(height: height);
  }

  /// 创建带字间距的文本样式
  static TextStyle withLetterSpacing(TextStyle style, double spacing) {
    return style.copyWith(letterSpacing: spacing);
  }
}

/// 文本主题扩展
extension AppTextTheme on TextTheme {
  /// 获取应用文本主题
  static TextTheme get appTextTheme => TextTheme(
    displayLarge: AppTypography.displayLarge,
    displayMedium: AppTypography.displayMedium,
    displaySmall: AppTypography.displaySmall,
    headlineLarge: AppTypography.headlineLarge,
    headlineMedium: AppTypography.headlineMedium,
    headlineSmall: AppTypography.headlineSmall,
    titleLarge: AppTypography.titleLarge,
    titleMedium: AppTypography.titleMedium,
    titleSmall: AppTypography.titleSmall,
    bodyLarge: AppTypography.bodyLarge,
    bodyMedium: AppTypography.bodyMedium,
    bodySmall: AppTypography.bodySmall,
    labelLarge: AppTypography.labelLarge,
    labelMedium: AppTypography.labelMedium,
    labelSmall: AppTypography.labelSmall,
  );
}
